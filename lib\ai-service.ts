/**
 * AI Service - Centralized AI integration for all planning operations
 * Replaces all mock data with real AI-powered responses
 */

interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  retryAfter?: number;
}

interface AIRequestConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  retries?: number;
  timeout?: number;
}

export class AIService {
  private apiKey: string | null = null;
  private baseUrl: string;
  private defaultConfig: AIRequestConfig;

  constructor() {
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.defaultConfig = {
      model: 'anthropic/claude-3.5-sonnet',
      temperature: 0.7,
      maxTokens: 4000,
      retries: 3,
      timeout: 30000
    };
  }

  private ensureApiKey(): void {
    if (!this.apiKey) {
      this.apiKey = process.env.OPENROUTER_API_KEY || '';
      if (!this.apiKey) {
        throw new Error('OPENROUTER_API_KEY environment variable is required');
      }
    }
  }

  /**
   * Make AI request with retry logic and proper error handling
   */
  private async makeAIRequest(
    prompt: string,
    systemPrompt: string,
    config: AIRequestConfig = {}
  ): Promise<AIResponse> {
    this.ensureApiKey(); // Ensure API key is available before making request
    const finalConfig = { ...this.defaultConfig, ...config };
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= finalConfig.retries!; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), finalConfig.timeout);

        const response = await fetch(`${this.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
            'X-Title': 'Linear Tasks Planning Agent'
          },
          body: JSON.stringify({
            model: finalConfig.model,
            messages: [
              { role: 'system', content: systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: finalConfig.temperature,
            max_tokens: finalConfig.maxTokens
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`AI API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }

        const data = await response.json();
        const content = data.choices?.[0]?.message?.content;

        if (!content) {
          throw new Error('No content received from AI API');
        }

        return {
          success: true,
          data: content
        };

      } catch (error) {
        lastError = error as Error;
        console.error(`AI request attempt ${attempt} failed:`, error);

        // Don't retry on certain errors
        if (error instanceof Error) {
          if (error.message.includes('401') || error.message.includes('403')) {
            break; // Authentication errors shouldn't be retried
          }
          if (error.message.includes('429')) {
            // Rate limit - exponential backoff
            await new Promise(resolve => setTimeout(resolve, Math.min(2000 * Math.pow(2, attempt), 30000)));
          }
          if (error.message.includes('timeout')) {
            // Timeout - shorter wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          }
        }

        if (attempt < finalConfig.retries!) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return {
      success: false,
      error: lastError?.message || 'AI request failed after all retries'
    };
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJSONResponse(content: string): any {
    try {
      // Clean up common AI response formatting issues
      let cleaned = content
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .replace(/^\s*[\r\n]+/gm, '')
        .trim();

      // Try to parse as-is first (most AI responses are already valid JSON)
      return JSON.parse(cleaned);
    } catch (error) {
      console.error('Failed to parse AI JSON response:', error);
      console.error('Raw content:', content.substring(0, 500) + '...');

      // Try to extract JSON from the content (AI often adds explanatory text before JSON)
      try {
        // Look for JSON object starting with { and ending with }
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          let extractedJson = jsonMatch[0];

          // Basic cleanup for common issues
          extractedJson = extractedJson
            .replace(/[\x00-\x1F]/g, '') // Remove control characters
            .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
            .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Quote unquoted keys
            .replace(/:\s*'([^']*)'/g, ': "$1"') // Convert single quotes to double quotes
            .trim();

          console.log('Attempting to parse extracted JSON...');
          return JSON.parse(extractedJson);
        }
      } catch (secondError) {
        console.error('Second JSON parse attempt failed:', secondError);
      }

      // Return a basic fallback structure
      console.log('All JSON parsing failed, returning fallback structure');
      return {
        structure: {
          folders: [
            {
              name: "src",
              purpose: "Main source code directory",
              subfolders: ["components", "pages", "utils", "styles"],
              keyFiles: ["App.tsx", "main.tsx"]
            }
          ]
        },
        keyFiles: [
          {
            path: "src/App.tsx",
            purpose: "Main application component",
            dependencies: [],
            priority: "high"
          }
        ],
        conventions: {
          naming: "camelCase for files, PascalCase for components",
          organization: "Feature-based organization",
          imports: "ES6 imports with absolute paths"
        },
        buildSystem: {
          configFiles: ["package.json", "tsconfig.json"],
          scripts: {
            dev: "npm run dev",
            build: "npm run build",
            test: "npm test"
          }
        }
      };
    }
  }

  /**
   * Analyze project prompt
   */
  async analyzePrompt(prompt: string, options: { designStyleGuide?: string, hasImages?: boolean } = {}): Promise<any> {
    const { designStyleGuide, hasImages } = options || {};

    let systemPrompt = `You are a senior project analyst with expertise in software development, product management, and technical architecture.

Analyze the given project prompt and extract comprehensive information. Return a JSON object with the following structure:

{
  "projectType": "web_app" | "mobile_app" | "desktop_app" | "game" | "api" | "library" | "other",
  "complexity": "low" | "medium" | "high" | "enterprise",
  "domain": "e-commerce" | "social" | "productivity" | "gaming" | "fintech" | "healthcare" | "education" | "other",
  "features": ["feature1", "feature2", ...],
  "technicalHints": ["hint1", "hint2", ...],
  "estimatedTimeframe": "1-2 weeks" | "1-2 months" | "3-6 months" | "6+ months",
  "teamSize": "solo" | "small (2-4)" | "medium (5-10)" | "large (10+)",
  "riskFactors": ["risk1", "risk2", ...],
  "successCriteria": ["criteria1", "criteria2", ...]
}

Be thorough and specific in your analysis. Extract as much meaningful information as possible.

IMPORTANT: Return only valid JSON. Do not include newlines, tabs, or control characters in string values.`;

    if (hasImages && designStyleGuide) {
      systemPrompt += `

ADDITIONAL CONTEXT: The user has provided design reference images that have been analyzed to create a comprehensive style guide. This style guide should be considered when analyzing the project requirements, as it indicates the user has specific design preferences and visual direction for the project.`;
    }

    let analysisPrompt = prompt;
    if (hasImages && designStyleGuide) {
      analysisPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designStyleGuide}

Consider this design context when analyzing the project requirements.`;
    }

    const response = await this.makeAIRequest(analysisPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to analyze prompt');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Generate clarification questions
   */
  async generateClarificationQuestions(prompt: string, analysis: any): Promise<any> {
    const systemPrompt = `You are a product manager expert at gathering requirements. Based on the project analysis, generate intelligent clarification questions.

Return a JSON object with this structure:

{
  "questions": [
    {
      "id": "unique_id",
      "question": "Clear, specific question",
      "type": "text" | "select" | "multiselect",
      "options": ["option1", "option2"] // only for select/multiselect
    }
  ],
  "priority": "high" | "medium" | "low",
  "reasoning": "Why these questions are important"
}

IMPORTANT: Return only valid JSON. Do not include newlines, tabs, or control characters in string values. Keep reasoning concise and on a single line.

Focus on the most critical unknowns that would significantly impact the project scope, timeline, or technical approach.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Generate 3-5 high-impact clarification questions.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to generate clarification questions');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Generate project summary
   */
  async generateSummary(prompt: string, analysis: any, clarifications: any = {}): Promise<any> {
    const systemPrompt = `You are a technical writer specializing in project documentation. Create a comprehensive project summary.

Return a JSON object with this structure:

{
  "overview": "Clear, concise project overview",
  "objectives": ["objective1", "objective2", ...],
  "scope": "Detailed scope description",
  "keyFeatures": ["feature1", "feature2", ...],
  "targetAudience": "Description of target users",
  "successMetrics": ["metric1", "metric2", ...],
  "constraints": ["constraint1", "constraint2", ...],
  "assumptions": ["assumption1", "assumption2", ...]
}

Be specific and actionable in your descriptions.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Clarifications: ${JSON.stringify(clarifications)}

Create a comprehensive project summary.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to generate summary');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Select technology stack
   */
  async selectTechStack(prompt: string, analysis: any, clarifications: any = {}): Promise<any> {
    const systemPrompt = `You are a senior technical architect with expertise in modern technology stacks. Recommend the optimal technology stack.

Return a JSON object with this structure:

{
  "frontend": {
    "framework": "React" | "Vue" | "Angular" | "Svelte" | "Next.js" | "other",
    "language": "TypeScript" | "JavaScript",
    "styling": "Tailwind CSS" | "Styled Components" | "CSS Modules" | "other",
    "reasoning": "Why this choice"
  },
  "backend": {
    "framework": "Node.js" | "Python" | "Go" | "Rust" | "Java" | "C#" | "other",
    "database": "PostgreSQL" | "MongoDB" | "MySQL" | "SQLite" | "other",
    "authentication": "Auth0" | "Firebase Auth" | "NextAuth" | "custom" | "other",
    "reasoning": "Why this choice"
  },
  "infrastructure": {
    "hosting": "Vercel" | "Netlify" | "AWS" | "Google Cloud" | "Azure" | "other",
    "cicd": "GitHub Actions" | "GitLab CI" | "Jenkins" | "other",
    "monitoring": "Sentry" | "LogRocket" | "DataDog" | "other",
    "reasoning": "Why this choice"
  },
  "additionalTools": ["tool1", "tool2", ...],
  "alternatives": {
    "considered": ["alternative1", "alternative2", ...],
    "reasoning": "Why alternatives were not chosen"
  }
}

Consider project complexity, team size, timeline, and specific requirements.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Clarifications: ${JSON.stringify(clarifications)}

Recommend the optimal technology stack.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to select tech stack');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Create Product Requirements Document
   */
  async createPRD(prompt: string, analysis: any, techStack: any, clarifications: any = {}): Promise<any> {
    const systemPrompt = `You are a senior product manager expert at creating comprehensive PRDs. Create a detailed Product Requirements Document.

Return a JSON object with this structure:

{
  "overview": "Executive summary of the product",
  "objectives": ["objective1", "objective2", ...],
  "userStories": [
    {
      "role": "user type",
      "goal": "what they want to do",
      "benefit": "why they want to do it"
    }
  ],
  "functionalRequirements": [
    {
      "id": "FR001",
      "title": "Requirement title",
      "description": "Detailed description",
      "priority": "high" | "medium" | "low",
      "acceptanceCriteria": ["criteria1", "criteria2", ...]
    }
  ],
  "nonFunctionalRequirements": [
    {
      "category": "performance" | "security" | "usability" | "scalability",
      "requirement": "Specific requirement",
      "metric": "Measurable metric"
    }
  ],
  "timeline": {
    "phases": [
      {
        "name": "Phase name",
        "duration": "X weeks",
        "deliverables": ["deliverable1", "deliverable2", ...]
      }
    ],
    "totalDuration": "X weeks/months"
  },
  "risks": [
    {
      "risk": "Risk description",
      "impact": "high" | "medium" | "low",
      "probability": "high" | "medium" | "low",
      "mitigation": "Mitigation strategy"
    }
  ]
}

Be comprehensive and specific.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Tech Stack: ${JSON.stringify(techStack)}

Clarifications: ${JSON.stringify(clarifications)}

Create a comprehensive PRD.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to create PRD');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Design wireframes
   */
  async designWireframes(prompt: string, analysis: any, prd: any): Promise<any> {
    const systemPrompt = `You are a UX/UI designer expert at creating wireframes. Design comprehensive wireframes for the project.

Return a JSON object with this structure:

{
  "pages": [
    {
      "name": "Page name",
      "type": "landing" | "dashboard" | "form" | "list" | "detail" | "other",
      "purpose": "What this page does",
      "wireframe": "ASCII art representation of the layout",
      "components": ["component1", "component2", ...],
      "interactions": ["interaction1", "interaction2", ...]
    }
  ],
  "components": [
    {
      "name": "Component name",
      "type": "header" | "navigation" | "form" | "card" | "modal" | "other",
      "description": "What this component does",
      "props": ["prop1", "prop2", ...]
    }
  ],
  "userFlow": [
    {
      "step": 1,
      "action": "User action",
      "page": "Page name",
      "result": "What happens"
    }
  ],
  "responsive": {
    "breakpoints": ["mobile", "tablet", "desktop"],
    "considerations": ["consideration1", "consideration2", ...]
  }
}

IMPORTANT:
- Return only valid JSON
- Use double quotes for all strings, never backticks or template literals
- For multi-line wireframes, use \\n for line breaks within the string
- Escape any special characters properly for JSON
- Do not use template literals (backticks) anywhere in the response`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

PRD: ${JSON.stringify(prd)}

Design comprehensive wireframes.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 6000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to design wireframes');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Plan file system structure
   */
  async planFileSystem(prompt: string, techStack: any, analysis: any, designContext?: string): Promise<any> {
    let systemPrompt = `You are a senior software architect expert at project structure. Plan a comprehensive file system structure.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.

Return a JSON object with this structure:

{
  "structure": {
    "folders": [
      {
        "name": "folder name",
        "purpose": "what this folder contains",
        "subfolders": ["subfolder1", "subfolder2", ...],
        "keyFiles": ["file1.ext", "file2.ext", ...]
      }
    ]
  },
  "keyFiles": [
    {
      "path": "relative/path/to/file.ext",
      "purpose": "what this file does",
      "dependencies": ["dependency1", "dependency2", ...],
      "priority": "high" | "medium" | "low"
    }
  ],
  "conventions": {
    "naming": "Naming convention rules",
    "organization": "How files are organized",
    "imports": "Import/export patterns"
  },
  "buildSystem": {
    "configFiles": ["config1", "config2", ...],
    "scripts": {
      "dev": "Development command",
      "build": "Build command",
      "test": "Test command"
    }
  }
}

Consider the chosen tech stack and project complexity.`;

    // Add design context if available
    if (designContext) {
      systemPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designContext}

Consider this design context when planning the file system structure. Include appropriate folders for assets, styles, components, and design-related files that align with the design requirements.`;
    }

    const contextPrompt = `Project: ${prompt}

Tech Stack: ${JSON.stringify(techStack)}

Analysis: ${JSON.stringify(analysis)}

Plan comprehensive file system structure.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to plan file system');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Define workflow logic
   */
  async defineWorkflow(prompt: string, analysis: any, prd: any, wireframes: any, designContext?: string): Promise<any> {
    let systemPrompt = `You are a systems architect expert at defining application workflows. Create comprehensive workflow definitions.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.

Return a JSON object with this structure:

{
  "userWorkflows": [
    {
      "name": "Workflow name",
      "description": "What this workflow accomplishes",
      "steps": [
        {
          "step": 1,
          "action": "User action",
          "system": "System response",
          "validation": "Validation rules",
          "errorHandling": "Error scenarios"
        }
      ],
      "triggers": ["trigger1", "trigger2", ...],
      "outcomes": ["outcome1", "outcome2", ...]
    }
  ],
  "systemWorkflows": [
    {
      "name": "System process name",
      "type": "background" | "scheduled" | "event-driven",
      "description": "What this process does",
      "steps": ["step1", "step2", ...],
      "dependencies": ["dependency1", "dependency2", ...]
    }
  ],
  "dataFlow": [
    {
      "source": "Data source",
      "destination": "Data destination",
      "transformation": "How data is transformed",
      "validation": "Validation rules"
    }
  ],
  "integrations": [
    {
      "service": "External service",
      "purpose": "Why we integrate",
      "dataExchange": "What data is exchanged",
      "errorHandling": "How errors are handled"
    }
  ]
}

Be specific about business logic and technical implementation.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.`;

    // Add design context if available
    if (designContext) {
      systemPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designContext}

Consider this design context when defining workflows. Ensure user workflows align with the design patterns and UI components specified in the style guide.`;
    }

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

PRD: ${JSON.stringify(prd)}

Wireframes: ${JSON.stringify(wireframes)}

Define comprehensive workflows. Return only valid JSON.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 6000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to define workflow');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Design database schema
   */
  async designDatabaseSchema(prompt: string, analysis: any, prd: any, techStack: any): Promise<any> {
    const systemPrompt = `You are a senior database architect expert at designing scalable database schemas. Create a comprehensive database schema design.

Return a JSON object with this structure:

{
  "databaseType": "relational" | "document" | "graph" | "hybrid",
  "tables": [
    {
      "name": "table_name",
      "purpose": "What this table stores",
      "columns": [
        {
          "name": "column_name",
          "type": "VARCHAR(255)" | "INTEGER" | "BOOLEAN" | "TIMESTAMP" | "TEXT" | "JSON" | "other",
          "constraints": ["PRIMARY KEY", "NOT NULL", "UNIQUE", "FOREIGN KEY", ...],
          "description": "What this column represents"
        }
      ],
      "indexes": [
        {
          "name": "index_name",
          "columns": ["column1", "column2"],
          "type": "btree" | "hash" | "gin" | "gist",
          "purpose": "Why this index is needed"
        }
      ],
      "relationships": [
        {
          "type": "one-to-many" | "many-to-many" | "one-to-one",
          "relatedTable": "related_table_name",
          "foreignKey": "foreign_key_column",
          "description": "Relationship description"
        }
      ]
    }
  ],
  "views": [
    {
      "name": "view_name",
      "purpose": "What this view provides",
      "query": "SQL query or description",
      "tables": ["table1", "table2", ...]
    }
  ],
  "migrations": [
    {
      "version": "001",
      "description": "Initial schema creation",
      "operations": ["CREATE TABLE users", "CREATE INDEX idx_users_email", ...]
    }
  ],
  "seedData": [
    {
      "table": "table_name",
      "description": "What seed data is needed",
      "examples": ["example1", "example2", ...]
    }
  ],
  "performance": {
    "considerations": ["consideration1", "consideration2", ...],
    "optimizations": ["optimization1", "optimization2", ...],
    "scalingStrategy": "How to scale the database"
  },
  "security": {
    "authentication": "How users are authenticated",
    "authorization": "How permissions are managed",
    "dataProtection": ["protection1", "protection2", ...],
    "compliance": ["GDPR", "HIPAA", "SOC2", ...]
  }
}

Consider the chosen technology stack, project requirements, and scalability needs.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

PRD: ${JSON.stringify(prd)}

Tech Stack: ${JSON.stringify(techStack)}

Design a comprehensive database schema. Return only valid JSON.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 6000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to design database schema');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Generate project scaffold
   */
  async generateProjectScaffold(prompt: string, analysis: any, techStack: any, filesystem: any, database: any): Promise<any> {
    const systemPrompt = `You are a senior software engineer expert at project scaffolding and code generation. Generate a comprehensive project scaffold with actual code files.

Return a JSON object with this structure:

{
  "projectStructure": {
    "rootFiles": [
      {
        "name": "package.json",
        "content": "actual file content as string",
        "description": "Package configuration with dependencies"
      }
    ],
    "folders": [
      {
        "name": "src",
        "files": [
          {
            "name": "index.js",
            "content": "actual code content",
            "description": "Main application entry point"
          }
        ]
      }
    ]
  },
  "setupInstructions": [
    {
      "step": 1,
      "title": "Install Dependencies",
      "command": "npm install",
      "description": "Install all required packages"
    }
  ],
  "environmentSetup": {
    "envVariables": [
      {
        "name": "DATABASE_URL",
        "description": "Database connection string",
        "example": "postgresql://user:pass@localhost:5432/dbname",
        "required": true
      }
    ],
    "configFiles": [
      {
        "name": ".env.example",
        "content": "DATABASE_URL=your_database_url_here",
        "description": "Environment variables template"
      }
    ]
  },
  "scripts": {
    "development": [
      {
        "name": "dev",
        "command": "npm run dev",
        "description": "Start development server"
      }
    ],
    "production": [
      {
        "name": "build",
        "command": "npm run build",
        "description": "Build for production"
      }
    ]
  },
  "documentation": {
    "readme": "Complete README.md content with setup instructions",
    "apiDocs": "API documentation if applicable",
    "deploymentGuide": "Step-by-step deployment instructions"
  },
  "nextSteps": [
    "Configure environment variables",
    "Set up database",
    "Run initial migrations",
    "Start development server"
  ]
}

IMPORTANT:
- Generate actual, working code content for files
- Include proper imports, exports, and basic functionality
- Use the chosen technology stack consistently
- Include error handling and best practices
- Make the scaffold immediately runnable after setup

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Tech Stack: ${JSON.stringify(techStack)}

File System: ${JSON.stringify(filesystem)}

Database: ${JSON.stringify(database)}

Generate a complete, working project scaffold with actual code files. Return only valid JSON.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 8000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to generate project scaffold');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Break down into implementation tasks
   */
  async breakdownTasks(prompt: string, allPreviousResults: any, designContext?: string): Promise<any> {
    let systemPrompt = `You are a senior project manager expert at breaking down projects into actionable tasks. Create a comprehensive task breakdown.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.

Return a JSON object with this structure:

{
  "phases": [
    {
      "name": "Phase name",
      "description": "What this phase accomplishes",
      "duration": "X weeks",
      "tasks": ["task_id1", "task_id2", ...]
    }
  ],
  "tasks": [
    {
      "id": "unique_task_id",
      "title": "Clear, actionable task title",
      "description": "Detailed task description",
      "category": "setup" | "frontend" | "backend" | "testing" | "deployment" | "documentation",
      "priority": "high" | "medium" | "low",
      "estimatedHours": 4,
      "dependencies": ["task_id1", "task_id2", ...],
      "skills": ["skill1", "skill2", ...],
      "deliverables": ["deliverable1", "deliverable2", ...],
      "acceptanceCriteria": ["criteria1", "criteria2", ...]
    }
  ],
  "milestones": [
    {
      "name": "Milestone name",
      "description": "What this milestone represents",
      "tasks": ["task_id1", "task_id2", ...],
      "deadline": "Week X"
    }
  ],
  "resources": {
    "teamSize": "Recommended team size",
    "roles": ["role1", "role2", ...],
    "tools": ["tool1", "tool2", ...]
  },
  "estimates": {
    "totalHours": 200,
    "totalWeeks": 8,
    "confidence": "high" | "medium" | "low"
  }
}

Create 20-40 specific, actionable tasks with realistic estimates.`;

    // Add design context if available
    if (designContext) {
      systemPrompt += `

DESIGN CONTEXT: The user has provided reference images with the following extracted style guide:

${designContext}

Consider this design context when breaking down tasks. Include specific tasks for implementing the design system, creating styled components, and ensuring the final implementation matches the provided design references.`;
    }

    const contextPrompt = `Project: ${prompt}

All Previous Results: ${JSON.stringify(allPreviousResults)}

Break down into comprehensive implementation tasks.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt, { maxTokens: 8000 });

    if (!response.success) {
      throw new Error(response.error || 'Failed to breakdown tasks');
    }

    return this.parseJSONResponse(response.data);
  }

  /**
   * Generate context profile for AI agents
   */
  async generateContextProfile(prompt: string, analysis: any, summary: any, prd: any): Promise<any> {
    const systemPrompt = `You are an AI systems architect expert at creating comprehensive context profiles for AI agents. Create a detailed context profile template.

CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting. Start your response directly with the opening brace {.

Return a JSON object with this exact structure:

{
  "profile_id": "agent-[domain]-[role]-v1.0.0",
  "identity": {
    "name": "[Agent Name based on project purpose]",
    "role": "[Specific Role based on project type]",
    "organization": "[Organization/Domain from project context]",
    "timezone": "UTC",
    "language": "en-US"
  },
  "goals": {
    "short_term": ["specific short-term goal 1", "specific short-term goal 2"],
    "long_term": ["specific long-term goal 1", "specific long-term goal 2"]
  },
  "preferences": {
    "communication_style": "[professional/casual/technical/friendly]",
    "response_format": "[structured_text/json/dashboard/report]",
    "tone": "[professional/helpful/authoritative/conversational]",
    "visuals": true,
    "default_output_type": "[text_response/structured_data/visual_report]"
  },
  "capabilities": {
    "tools_enabled": ["tool1", "tool2", "tool3"],
    "environment": {
      "platform": "[Cloud-based/On-premise/Hybrid]",
      "extensions": ["extension1", "extension2"]
    }
  },
  "memory": {
    "scope": "[conversation/session/persistent/company-specific]",
    "persistence": "[session/temporary/persistent]",
    "structure": "[contextual/relational_db/vector_db]",
    "data_points": ["data_point1", "data_point2", "data_point3"]
  },
  "constraints": {
    "rate_limit": "[number] requests/hour",
    "budget": {
      "monthly": 250,
      "used": 0
    },
    "operational_constraints": ["constraint1", "constraint2"]
  },
  "behavioral_flags": {
    "debug_mode": false,
    "auto_summarize": true,
    "use_context_window": true,
    "custom_flag_1": false,
    "custom_flag_2": true
  },
  "metadata": {
    "created_at": "[current ISO timestamp]",
    "last_updated": "[current ISO timestamp]",
    "version": "1.0.0"
  }
}

Base the profile on the project analysis and create specific, relevant values for each field.`;

    const contextPrompt = `Project: ${prompt}

Analysis: ${JSON.stringify(analysis)}

Summary: ${JSON.stringify(summary)}

PRD: ${JSON.stringify(prd)}

Generate a comprehensive context profile template for this AI agent project.`;

    const response = await this.makeAIRequest(contextPrompt, systemPrompt);

    if (!response.success) {
      throw new Error(response.error || 'Failed to generate context profile');
    }

    return this.parseJSONResponse(response.data);
  }
}

// Export singleton instance
export const aiService = new AIService();
